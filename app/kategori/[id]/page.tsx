'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft } from 'lucide-react';

// Statik kategoriler (CategorySection'dan)
const staticCategories = [
    {
        id: 'kebap',
        name: '<PERSON><PERSON><PERSON>',
        imageUrl: '/images/adana.jpg'
    },
    {
        id: 'burger',
        name: '<PERSON>',
        imageUrl: '/images/burger.jpg'
    },
    {
        id: 'pizza',
        name: 'Pizza',
        imageUrl: '/images/pizza.jpg'
    },
    {
        id: 'tatli',
        name: 'Tatl<PERSON>',
        imageUrl: '/images/tatlim.jpg'
    },
    {
        id: 'icecek',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        imageUrl: '/images/icecekler.jpg'
    },
    {
        id: 'kahvalti',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        imageUrl: '/images/kahvalti.jpg'
    }
];

export default function CategoryPage() {
    const params = useParams();
    const categoryId = params.id as string;

    // Önce statik kategori kontrolü yap
    const staticCategory = staticCategories.find(cat => cat.id === categoryId);

    if (!staticCategory) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center">
                    <div className="text-red-500 text-lg mb-4">Kategori bulunamadı.</div>
                    <Link
                        href="/"
                        className="inline-flex items-center gap-2 text-brand-blue hover:underline"
                    >
                        <ArrowLeft size={20} />
                        Ana Sayfaya Dön
                    </Link>
                </div>
            </div>
        );
    }



    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
                <Link
                    href="/"
                    className="inline-flex items-center gap-2 text-brand-blue hover:underline mb-4"
                >
                    <ArrowLeft size={20} />
                    Ana Sayfa
                </Link>

                <h1 className="text-3xl font-bold text-brand-blue mb-2">
                    {staticCategory.name}
                </h1>
                <p className="text-gray-600">
                    Bu kategoride henüz restoran bulunmuyor.
                </p>
            </div>

            {/* Category Hero Image */}
            <div className="relative h-64 rounded-xl overflow-hidden mb-8">
                <Image
                    src={staticCategory.imageUrl}
                    alt={staticCategory.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 1200px) 100vw, 1200px"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <h2 className="text-4xl font-bold text-white">{staticCategory.name}</h2>
                </div>
            </div>

            {/* Empty State */}
            <div className="text-center py-12">
                <div className="text-gray-500 text-lg mb-4">
                    Bu kategoride henüz restoran bulunmuyor.
                </div>
                <Link
                    href="/"
                    className="inline-flex items-center gap-2 text-brand-blue hover:underline"
                >
                    <ArrowLeft size={20} />
                    Ana Sayfaya Dön
                </Link>
            </div>
        </div>
    );
}
