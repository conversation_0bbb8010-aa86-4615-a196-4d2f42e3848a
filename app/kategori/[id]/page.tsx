'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useFirestore } from '@/hooks/useFirestore';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft } from 'lucide-react';

interface MasterCategory {
    id: string;
    name: string;
    imageUrl: string;
}

interface Firm {
    id: string;
    name: string;
    logo: string;
    city: string;
    district: string;
    isReservation: boolean;
    isTakeAway: boolean;
    minReservationAmount: string;
    masterCategories?: string[];
    categories?: string[];
}

// Statik kategoriler (CategorySection'dan)
const staticCategories = [
    {
        id: 'kebap',
        name: 'Keba<PERSON>',
        imageUrl: '/images/adana.jpg'
    },
    {
        id: 'burger',
        name: 'Burger',
        imageUrl: '/images/burger.jpg'
    },
    {
        id: 'pizza',
        name: 'Pizza',
        imageUrl: '/images/pizza.jpg'
    },
    {
        id: 'tatli',
        name: 'Ta<PERSON><PERSON>',
        imageUrl: '/images/tatlim.jpg'
    },
    {
        id: 'icecek',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        imageUrl: '/images/icecekler.jpg'
    },
    {
        id: 'kahvalti',
        name: 'Kahvaltı',
        imageUrl: '/images/kahvalti.jpg'
    }
];



export default function CategoryPage() {
    const params = useParams();
    const categoryId = params.id as string;

    const [category, setCategory] = useState<MasterCategory | null>(null);
    const [filteredFirms, setFilteredFirms] = useState<Firm[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const { getOne: getMasterCategory } = useFirestore<MasterCategory>('mastercategories');
    const { getAll: getAllFirms } = useFirestore<Firm>('firms');

    useEffect(() => {
        let mounted = true;

        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                console.log('🔍 Fetching data for category ID:', categoryId);

                // Önce statik kategorilerde ara
                const staticCategory = staticCategories.find(cat => cat.id === categoryId);
                console.log('📂 Static category found:', staticCategory);

                let categoryData = null;

                if (staticCategory) {
                    // Statik kategori bulundu
                    categoryData = staticCategory;
                    console.log('✅ Using static category:', categoryData);
                } else {
                    // Master kategori bilgilerini çek
                    categoryData = await getMasterCategory(categoryId);
                    console.log('📂 Master category data:', categoryData);
                }

                if (!mounted) return;

                if (!categoryData) {
                    console.log('❌ Category not found in both static and master categories');
                    setError('Kategori bulunamadı.');
                    return;
                }

                setCategory(categoryData);

                // Tüm firmaları çek
                const firmsData = await getAllFirms();
                console.log('🏢 All firms data:', firmsData);
                console.log('🏢 Firms count:', firmsData?.length || 0);

                if (!mounted) return;

                if (firmsData) {
                    // Bu kategoriye ait firmaları filtrele
                    const filtered = firmsData.filter(firm => {
                        console.log('🔍 Checking firm:', firm.name);
                        console.log('   - masterCategories:', firm.masterCategories);
                        console.log('   - categories:', firm.categories);

                        // Master kategorilerde ara
                        const inMasterCategories = firm.masterCategories &&
                                                 firm.masterCategories.includes(categoryId);

                        // Normal kategorilerde ara (statik kategoriler için)
                        const inCategories = firm.categories &&
                                           firm.categories.includes(categoryId);

                        const result = inMasterCategories || inCategories;
                        console.log('   - Match result:', result);

                        return result;
                    });

                    console.log('✅ Filtered firms:', filtered);
                    console.log('✅ Filtered count:', filtered.length);
                    setFilteredFirms(filtered);
                }

            } catch (error) {
                if (mounted) {
                    console.error('❌ Error fetching data:', error);
                    setError('Veriler yüklenirken bir hata oluştu.');
                }
            } finally {
                if (mounted) {
                    setLoading(false);
                }
            }
        };

        fetchData();

        return () => {
            mounted = false;
        };
    }, [categoryId, getMasterCategory, getAllFirms]);

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center">
                    <div className="text-red-500 text-lg mb-4">{error}</div>
                    <Link
                        href="/"
                        className="inline-flex items-center gap-2 text-brand-blue hover:underline"
                    >
                        <ArrowLeft size={20} />
                        Ana Sayfaya Dön
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
                <Link
                    href="/"
                    className="inline-flex items-center gap-2 text-brand-blue hover:underline mb-4"
                >
                    <ArrowLeft size={20} />
                    Ana Sayfa
                </Link>

                {loading ? (
                    <div className="animate-pulse">
                        <div className="h-8 bg-gray-200 rounded w-64 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-48"></div>
                    </div>
                ) : category && (
                    <>
                        <h1 className="text-3xl font-bold text-brand-blue mb-2">
                            {category.name}
                        </h1>
                        <p className="text-gray-600">
                            {filteredFirms.length} restoran bulundu
                        </p>
                    </>
                )}
            </div>

            {/* Category Hero Image */}
            {!loading && category && (
                <div className="relative h-64 rounded-xl overflow-hidden mb-8">
                    <Image
                        src={category.imageUrl}
                        alt={category.name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 1200px) 100vw, 1200px"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                        <h2 className="text-4xl font-bold text-white">{category.name}</h2>
                    </div>
                </div>
            )}

            {/* Restaurants List */}
            {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, index) => (
                        <div key={index} className="rounded-xl overflow-hidden shadow-md border border-gray-100 animate-pulse">
                            <div className="h-48 bg-gray-200"></div>
                            <div className="p-4">
                                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                                <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                                <div className="flex gap-2">
                                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : filteredFirms.length === 0 ? (
                <div className="text-center py-12">
                    <div className="text-gray-500 text-lg mb-4">
                        Bu kategoride henüz restoran bulunmuyor.
                    </div>
                    <Link
                        href="/"
                        className="inline-flex items-center gap-2 text-brand-blue hover:underline"
                    >
                        <ArrowLeft size={20} />
                        Ana Sayfaya Dön
                    </Link>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredFirms.map((firm) => (
                        <Link
                            href={`/restoran/${firm.id}`}
                            key={firm.id}
                            className="group rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100"
                        >
                            <div className="relative h-48">
                                <Image
                                    src={firm.logo}
                                    alt={firm.name}
                                    fill
                                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                />
                                {firm.minReservationAmount && (
                                    <div className="absolute bottom-4 right-4 bg-white px-3 py-1 rounded-full text-sm shadow-md">
                                        Min. {firm.minReservationAmount}₺
                                    </div>
                                )}
                            </div>

                            <div className="p-4">
                                <h3 className="font-semibold text-lg mb-1 group-hover:text-brand-blue transition-colors">
                                    {firm.name}
                                </h3>
                                <p className="text-gray-600 text-sm mb-2">{firm.district}, {firm.city}</p>
                                <div className="flex flex-wrap gap-2">
                                    {firm.isReservation && (
                                        <span className="text-xs px-2 py-1 rounded-full bg-brand-blue/10 text-brand-blue">
                                            Rezervasyon
                                        </span>
                                    )}
                                    {firm.isTakeAway && (
                                        <span className="text-xs px-2 py-1 rounded-full bg-brand-orange/10 text-brand-orange">
                                            Gel-Al
                                        </span>
                                    )}
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
}
